package com.hzjm.service.model.DTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/9/8
 * @description: 获取不在架销售数据请求
 */
@Data
public class GetNotOnSaleProductReq implements Serializable {

    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize = 20;
}
