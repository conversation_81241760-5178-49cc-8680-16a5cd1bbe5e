package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.mapper.SysProdMapper;
import com.hzjm.service.model.DTO.GetNotOnSaleProductReq;
import com.hzjm.service.model.VO.NotOnSaleProductVo;
import com.hzjm.service.service.INotOnSaleProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/9/8
 * @description: 不在架商品服务实现类
 */
@Slf4j
@Service
public class NotOnSaleProductServiceImpl implements INotOnSaleProductService {

    @Resource
    private SysProdMapper sysProdMapper;

    @Override
    public IPage<NotOnSaleProductVo> getNotOnSaleProducts(GetNotOnSaleProductReq req) {
        LambdaQueryWrapper<SysProd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SysProd::getOneId);
        queryWrapper.and(wrapper -> wrapper.isNull(SysProd::getStatus).or().ne(SysProd::getStatus, 2));
        queryWrapper.orderByDesc(SysProd::getGmtModify);
        LambdaQueryWrapper<SysProd> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.and(wrapper -> wrapper.isNull(SysProd::getStatus).or().ne(SysProd::getStatus, 2));
        long total = sysProdMapper.selectCount(countWrapper);
        long offset = ((long) req.getPageNum() - 1) * req.getPageSize();
        queryWrapper.last("LIMIT " + offset + ", " + req.getPageSize());
        List<SysProd> records = sysProdMapper.selectList(queryWrapper);
        List<NotOnSaleProductVo> voRecords = records.stream().map(this::convertToVo).collect(Collectors.toList());
        Page<NotOnSaleProductVo> resultPage = new Page<>();
        resultPage.setRecords(voRecords);
        resultPage.setTotal(total);
        resultPage.setCurrent(req.getPageNum());
        resultPage.setSize(voRecords.size());
        long pages = total == 0 ? 0 : (total + req.getPageSize() - 1) / req.getPageSize();
        resultPage.setPages(pages);
        resultPage.setSearchCount(false);
        return resultPage;
    }

    /**
     * 将SysProd转换为NotOnSaleProductVo
     */
    private NotOnSaleProductVo convertToVo(SysProd sysProd) {
        NotOnSaleProductVo vo = new NotOnSaleProductVo();
        vo.setOneId(sysProd.getOneId());
        return vo;
    }
}
