package com.hzjm.tts.common;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.hzjm.common.infrastructure.HttpRequest;
import com.hzjm.common.infrastructure.exception.ApiUnauthorizedException;
import com.hzjm.common.infrastructure.service.ApiRequestServiceImpl;
import com.hzjm.common.infrastructure.service.httprequest.ApiToken;
import com.hzjm.common.model.BaseException;
import com.hzjm.tts.auth.TTSTokenGuardian;
import com.hzjm.tts.infrastructure.TTSRequestSigner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class TTSApiRequestServiceImpl extends ApiRequestServiceImpl {

    @Autowired
    TTSTokenGuardian ttsTokenGuardian;

    public <T> T doRequestWithAccount(HttpRequest<T> request, String accountEmail) throws BaseException {

        ApiToken apiToken = ttsTokenGuardian.getApiTokenByEmail(accountEmail);
        if (ObjectUtils.isEmpty(apiToken.getAccessToken()) || ObjectUtils.isEmpty(apiToken.getApiKey())) {
            throw new ApiUnauthorizedException(401, "Failed to get JwtToken (Access Token) from TTS, please check.");
        }

        // 根据 tts 的需求 每一个请求都需要塞入数据
        // 根据 content type 是否包含 "application/json" 来判断，不包含也当作是 json 类型
        boolean isJsonType = !ObjectUtils.isNotEmpty(request.getHeaders().get("Content-Type")) || request.getHeaders().get("Content-Type").contains("application/json");

        // 设置 common parameters
        Map<String, String> queryParameters = request.getQueryParameters();
        // 检查 queryParameters 是否为 null
        if (queryParameters == null) {
            // 如果为 null, 创建一个新的 HashMap 实例
            queryParameters = new HashMap<>();
        }
        queryParameters.put("app_key", apiToken.getApiKey());
        queryParameters.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));

        String sign = TTSRequestSigner.generateSignature(
                request.getPath(),
                queryParameters,
                request.getBody(),
                isJsonType,
                apiToken.getClientSecret());
        queryParameters.put("sign", sign);

        request.setQueryParameters(queryParameters);

        // 设置 access token
        request.getHeaders().put("x-tts-access-token", apiToken.getAccessToken());

        return super.doRequest(request);
    }

    public <T> T uploadFileWithAccount(HttpRequest<T> request, File file, String accountEmail) throws BaseException {
        ApiToken apiToken = ttsTokenGuardian.getApiTokenByEmail(accountEmail);
        if (ObjectUtils.isEmpty(apiToken.getAccessToken()) || ObjectUtils.isEmpty(apiToken.getApiKey())) {
            throw new ApiUnauthorizedException(401, "Failed to get JwtToken (Access Token) from TTS, please check.");
        }

        // 设置 common parameters
        // 生成 请求签名
        Map<String, String> queryParameters = request.getQueryParameters();
        // 检查 queryParameters 是否为 null
        if (queryParameters == null) {
            // 如果为 null, 创建一个新的 HashMap 实例
            queryParameters = new HashMap<>();
        }
        queryParameters.put("app_key", apiToken.getApiKey());
        queryParameters.put("timestamp", String.valueOf(Instant.now().getEpochSecond()));

        String sign = TTSRequestSigner.generateSignature(
                request.getPath(),
                queryParameters,
                request.getBody(),
                false,
                apiToken.getClientSecret());
        queryParameters.put("sign", sign);

        request.setQueryParameters(queryParameters);

        // 设置 access token
        request.getHeaders().put("x-tts-access-token", apiToken.getAccessToken());

        return super.uploadFile(request, file);
    }

}
