package com.hzjm.tts.order.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * List of arbitration statuses.
 * Available values:
 * - IN_PROGRESS: The TikTok Shop platform operator is processing arbitration. Platform may request additional information from the seller.
 * - SUPPORT_BUYER: The platform operator supports buyer.
 * - SUPPORT_SELLER: The platform operator supports seller.
 * - CLOSED: Arbitration is closed.
 */
@Getter
@AllArgsConstructor
public enum ArbitrationStatus {

    IN_PROGRESS("IN_PROGRESS"),
    SUPPORT_BUYER("SUPPORT_BUYER"),
    SUPPORT_SELLER("SUPPORT_SELLER"),
    CLOSED("CLOSED");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }
}
