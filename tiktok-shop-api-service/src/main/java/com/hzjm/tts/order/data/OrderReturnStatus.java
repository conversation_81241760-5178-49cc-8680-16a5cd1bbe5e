package com.hzjm.tts.order.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * List of return status.
 * Available values:
 * - RETURN_OR_REFUND_REQUEST_PENDING: Buyer has initiated a return or refund request. The request is pending review by seller or system.
 * - REFUND_OR_RETURN_REQUEST_REJECT: The return or refund request was rejected.
 * - AWAITING_BUYER_SHIP: The return request was approved. The seller is waiting for the buyer to ship the approved items to the seller. If the buyer doesn't ship the items to the seller before the deadline, the platform will close the request.
 * - BUYER_SHIPPED_ITEM: Buyer has shipped the approved items to seller.
 * - REJECT_RECEIVE_PACKAGE: Seller inspected the returned items and rejected the return request.
 * - RETURN_OR_REFUND_REQUEST_SUCCESS: The return/refund request was successful. The buyer will be refunded.
 * - RETURN_OR_REFUND_REQUEST_CANCEL: The request has been cancelled by the buyer or system.
 * - RETURN_OR_REFUND_REQUEST_COMPLETE: The return/refund was processed successfully. The buyer has been refunded.
 * - AWAITING_BUYER_RESPONSE: Seller offer another return type to the buyer, and waiting buyer response. Seller proposed return type can check the seller_proposed_return_type.
 */
@Getter
@AllArgsConstructor
public enum OrderReturnStatus {

    RETURN_OR_REFUND_REQUEST_PENDING("RETURN_OR_REFUND_REQUEST_PENDING"),
    REFUND_OR_RETURN_REQUEST_REJECT("REFUND_OR_RETURN_REQUEST_REJECT"),
    AWAITING_BUYER_SHIP("AWAITING_BUYER_SHIP"),
    BUYER_SHIPPED_ITEM("BUYER_SHIPPED_ITEM"),
    REJECT_RECEIVE_PACKAGE("REJECT_RECEIVE_PACKAGE"),
    RETURN_OR_REFUND_REQUEST_SUCCESS("RETURN_OR_REFUND_REQUEST_SUCCESS"),
    RETURN_OR_REFUND_REQUEST_CANCEL("RETURN_OR_REFUND_REQUEST_CANCEL"),
    RETURN_OR_REFUND_REQUEST_COMPLETE("RETURN_OR_REFUND_REQUEST_COMPLETE"),
    AWAITING_BUYER_RESPONSE("AWAITING_BUYER_RESPONSE");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }

}
