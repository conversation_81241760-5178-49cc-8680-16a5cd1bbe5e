package com.hzjm.tts.order.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * List of return types.
 * Available values:
 * - REFUND
 * - RETURN_AND_REFUND
 * - REPLACEMENT
 */
@Getter
@AllArgsConstructor
public enum OrderReturnType {

    REFUND("REFUND"),
    RETURN_AND_REFUND("RETURN_AND_REFUND"),
    REPLACEMENT("REPLACEMENT");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }
}
