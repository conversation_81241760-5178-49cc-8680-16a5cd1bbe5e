package com.hzjm.tts.order.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ShippingType {

    TIKTOK("TIKTOK"),
    SELLER("SELLER");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }
}
