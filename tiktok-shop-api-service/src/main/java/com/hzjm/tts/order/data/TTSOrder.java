package com.hzjm.tts.order.data;

import lombok.Data;

import java.io.Serializable;

/**
 * {
 *         "auto_combine_group_id": "12345677",
 *         "buyer_email": "<EMAIL>\n",
 *         "buyer_message": "Please ship asap!",
 *         "cancel_order_sla_time": **********,
 *         "cancel_reason": "Pricing error",
 *         "cancel_time": **********,
 *         "cancellation_initiator": "SELLER",
 *         "collection_due_time": **********,
 *         "collection_time": **********,
 *         "commerce_platform": "TIKTOK_SHOP",
 *         "cpf": "3213-31231412",
 *         "cpf_name": "<PERSON>",
 *         "create_time": 1619611561,
 *         "delivery_due_time": **********,
 *         "delivery_option_id": "7091146663229654785",
 *         "delivery_option_name": "Standard Shipping",
 *         "delivery_option_required_delivery_time": **********,
 *         "delivery_sla_time": **********,
 *         "delivery_time": **********,
 *         "delivery_type": "HOME_DELIVERY",
 *         "fast_dispatch_sla_time": **********,
 *         "fulfillment_type": "FULFILLMENT_BY_SELLER",
 *         "handling_duration": {
 *           "days": "7",
 *           "type": "BUSINESS_DAY"
 *         },
 *         "has_updated_recipient_address": false,
 *         "id": "576461413038785752",
 *         "is_buyer_request_cancel": false,
 *         "is_cod": false,
 *         "is_on_hold_order": false,
 *         "is_replacement_order": false,
 *         "is_sample_order": "FALSE\n",
 *         "line_items": [
 *           {
 *             "buyer_service_fee": "1000",
 *             "cancel_reason": "Discount not as expected",
 *             "cancel_user": "BUYER",
 *             "combined_listing_skus": [
 *               {
 *                 "product_id": "1729582718312380456",
 *                 "seller_sku": "yellow-24-XL ",
 *                 "sku_count": 1,
 *                 "sku_id": "2729382476852921123"
 *               }
 *             ],
 *             "currency": "IDR",
 *             "display_status": "UNPAID",
 *             "handling_duration_days": "7",
 *             "id": "577086512123755123",
 *             "is_dangerous_good": true,
 *             "is_gift": false,
 *             "item_tax": [
 *               {
 *                 "tax_amount": "21.2",
 *                 "tax_rate": "0.35",
 *                 "tax_type": "SALES_TAX"
 *               }
 *             ],
 *             "original_price": "0.01",
 *             "package_id": "1153132168123859123\n",
 *             "package_status": "TO_FULFILL",
 *             "platform_discount": "0",
 *             "product_id": "1729582718312380123",
 *             "product_name": "Women's Winter Crochet Clothes",
 *             "retail_delivery_fee": "1.28",
 *             "rts_time": **********,
 *             "sale_price": "0.01",
 *             "seller_discount": "0",
 *             "seller_sku": "red_iphone_256",
 *             "shipping_provider_id": "6617675021119438849",
 *             "shipping_provider_name": "TT Virtual express",
 *             "sku_id": "2729382476852921560",
 *             "sku_image": "https://p16-oec-va.itexeitg.com/tos-maliva-d-o5syd03w52-us/46123e87d14f40b69b839",
 *             "sku_name": "Iphone",
 *             "sku_type": "PRE_ORDER",
 *             "small_order_fee": "5000",
 *             "tracking_number": "JX12345"
 *           }
 *         ],
 *         "need_upload_invoice": "NEED_INVOICE\n",
 *         "order_type": "ZERO_LOTTERY",
 *         "packages": [
 *           {
 *             "id": "1152321127278713123"
 *           }
 *         ],
 *         "paid_time": **********,
 *         "payment": {
 *           "buyer_service_fee": "1000",
 *           "currency": "IDR",
 *           "handling_fee": "1000",
 *           "item_insurance_fee": "1000",
 *           "original_shipping_fee": "5000",
 *           "original_total_product_price": "5000",
 *           "platform_discount": "5000",
 *           "product_tax": "21.3",
 *           "retail_delivery_fee": "1.28",
 *           "seller_discount": "5000",
 *           "shipping_fee": "5000",
 *           "shipping_fee_cofunded_discount": "5000",
 *           "shipping_fee_platform_discount": "5000",
 *           "shipping_fee_seller_discount": "5000",
 *           "shipping_fee_tax": "11",
 *           "shipping_insurance_fee": "1000",
 *           "small_order_fee": "3000",
 *           "sub_total": "5000",
 *           "tax": "5000",
 *           "total_amount": "5000"
 *         },
 *         "payment_method_name": "CCDC",
 *         "pick_up_cut_off_time": **********,
 *         "recipient_address": {
 *           "address_detail": "Unit one building 8",
 *           "address_line1": "TikTok 5800 bristol Pkwy",
 *           "address_line2": "Suite 100",
 *           "address_line3": " ",
 *           "address_line4": " ",
 *           "delivery_preferences": {
 *             "drop_off_location": "Front Door"
 *           },
 *           "district_info": [
 *             {
 *               "address_level": "L0",
 *               "address_level_name": "Country",
 *               "address_name": "United Kingdom"
 *             }
 *           ],
 *           "first_name": "David",
 *           "full_address": "1199 Coleman Ave San Jose, CA 95110",
 *           "last_name": "Kong",
 *           "name": "David Kong",
 *           "phone_number": "(+1)213-***-1234",
 *           "postal_code": "95110",
 *           "region_code": "US"
 *         },
 *         "release_date": **********,
 *         "replaced_order_id": "576461416728782174",
 *         "request_cancel_time": **********,
 *         "rts_sla_time": **********,
 *         "rts_time": **********,
 *         "seller_note": "seller note",
 *         "shipping_due_time": **********,
 *         "shipping_provider": "TT Virtual express",
 *         "shipping_provider_id": "6617675021119438849",
 *         "shipping_type": "TIKTOK",
 *         "split_or_combine_tag": "COMBINED",
 *         "status": "UNPAID",
 *         "tracking_number": "JX12345",
 *         "tts_sla_time": **********,
 *         "update_time": **********,
 *         "user_id": "7021436810468230477",
 *         "warehouse_id": "6955005333819123123"
 *       }
 */
import java.util.List;

@Data
public class TTSOrder implements Serializable {
    private String auto_combine_group_id;
    private String buyer_email;
    private String buyer_message;
    private long cancel_order_sla_time;
    private String cancel_reason;
    private long cancel_time;
    private String cancellation_initiator;
    private long collection_due_time;
    private long collection_time;
    private String commerce_platform;
    private String cpf;
    private String cpf_name;
    private long create_time;
    private long delivery_due_time;
    private String delivery_option_id;
    private String delivery_option_name;
    private long delivery_option_required_delivery_time;
    private long delivery_sla_time;
    private long delivery_time;
    private String delivery_type;
    private long fast_dispatch_sla_time;
    private String fulfillment_type;
    private HandlingDuration handling_duration;
    private boolean has_updated_recipient_address;
    private String id;
    private boolean is_buyer_request_cancel;
    private boolean is_cod;
    private boolean is_on_hold_order;
    private boolean is_replacement_order;
    private boolean is_sample_order;
    private List<LineItem> line_items;
    private String need_upload_invoice;
    private String order_type;
    private List<Package> packages;
    private long paid_time;
    private Payment payment;
    private String payment_method_name;
    private long pick_up_cut_off_time;
    private RecipientAddress recipient_address;
    private long release_date;
    private String replaced_order_id;
    private long request_cancel_time;
    private long rts_sla_time;
    private long rts_time;
    private String seller_note;
    private long shipping_due_time;
    private String shipping_provider;
    private String shipping_provider_id;
    private ShippingType shipping_type;
    private String split_or_combine_tag;
    private TTSOrderStatus status;
    private String tracking_number;
    private long tts_sla_time;
    private long update_time;
    private String user_id;
    private String warehouse_id;

    @Data
    public static class HandlingDuration {
        private String days;
        private String type;
    }

    @Data
    public static class LineItem {
        private String buyer_service_fee;
        private String cancel_reason;
        private String cancel_user;
        private List<CombinedListingSku> combined_listing_skus;
        private String currency;
        private TTSOrderStatus display_status;
        private String handling_duration_days;
        private String id;
        private boolean is_dangerous_good;
        private boolean is_gift;
        private List<ItemTax> item_tax;
        private String original_price;
        private String package_id;
        private String package_status;
        private String platform_discount;
        private String product_id;
        private String product_name;
        private String retail_delivery_fee;
        private long rts_time;
        private String sale_price;
        private String seller_discount;
        private String seller_sku;
        private String shipping_provider_id;
        private String shipping_provider_name;
        private String sku_id;
        private String sku_image;
        private String sku_name;
        private String sku_type;
        private String small_order_fee;
        private String tracking_number;

        @Data
        public static class CombinedListingSku {
            private String product_id;
            private String seller_sku;
            private int sku_count;
            private String sku_id;
        }

        @Data
        public static class ItemTax {
            private String tax_amount;
            private String tax_rate;
            private String tax_type;
        }
    }

    @Data
    public static class Package {
        private String id;
    }

    @Data
    public static class Payment {
        private String buyer_service_fee;
        private String currency;
        private String handling_fee;
        private String item_insurance_fee;
        private String original_shipping_fee;
        private String original_total_product_price;
        private String platform_discount;
        private String product_tax;
        private String retail_delivery_fee;
        private String seller_discount;
        private String shipping_fee;
        private String shipping_fee_cofunded_discount;
        private String shipping_fee_platform_discount;
        private String shipping_fee_seller_discount;
        private String shipping_fee_tax;
        private String shipping_insurance_fee;
        private String small_order_fee;
        private String sub_total;
        private String tax;
        private String total_amount;
    }

    @Data
    public static class RecipientAddress {
        private String address_detail;
        private String address_line1;
        private String address_line2;
        private String address_line3;
        private String address_line4;
        private DeliveryPreferences delivery_preferences;
        private String first_name;
        private String full_address;
        private String last_name;
        private String name;
        private String phone_number;
        private String postal_code;
        private String region_code;
        private List<DistrictInfo> district_info;

        @Data
        public static class DeliveryPreferences {
            private String drop_off_location;
        }

        @Data
        public static class DistrictInfo {
            private String address_level;
            private String address_level_name;
            private String address_name;
        }
    }
}
