package com.hzjm.tts.order.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Specific order status.
 * Available values:
 * - UNPAID: The order has been placed, but payment has not been completed.
 * - ON_HOLD: The order has been accepted and is awaiting fulfillment. The buyer may still cancel without the seller’s approval. If order_type=PRE_ORDER, the product is still awaiting release so payment will only be authorized 1 day before the release, but the seller should start preparing for the release. (Applicable only for the US and UK market).
 * - AWAITING_SHIPMENT: The order is ready to be shipped, but no items have been shipped yet.
 * - PARTIALLY_SHIPPING: Some items in the order have been shipped, but not all.
 * - AWAITING_COLLECTION: Shipping has been arranged, but the package is waiting to be collected by the carrier.
 * - IN_TRANSIT: The package has been collected by the carrier and delivery is in progress.
 * - DELIVERED: The package has been delivered to the buyer.
 * - COMPLETED: The order has been completed, and no further returns or refunds are allowed.
 * - CANCELLED: The order has been cancelled.
 */
@Getter
@AllArgsConstructor
public enum TTSOrderStatus {

    UNPAID("UNPAID"),
    ON_HOLD("ON_HOLD"),
    AWAITING_SHIPMENT("AWAITING_SHIPMENT"),
    PARTIALLY_SHIPPING("PARTIALLY_SHIPPING"),
    AWAITING_COLLECTION("AWAITING_COLLECTION"),
    IN_TRANSIT("IN_TRANSIT"),
    DELIVERED("DELIVERED"),
    COMPLETED("COMPLETED"),
    CANCELLED("CANCELLED");

    @EnumValue
    @JsonValue
    public final String rawValue;

    @Override
    public String toString() {
        return this.rawValue;
    }
}
