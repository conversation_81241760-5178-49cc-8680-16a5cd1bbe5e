package com.hzjm.tts.order.data.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {
 *     "order_id": "5793990727963214852",
 *     "order_create_time": 1685548800,
 *     "currency": "GBP",
 *     "revenue_amount": "200",
 *     "fee_and_tax_amount": "-30",
 *     "shipping_cost_amount": "-70",
 *     "settlement_amount": "130",
 *     "sku_transactions": [
 *       {
 *         "sku_id": "1636700041413599290",
 *         "sku_name": "Test SKU name",
 *         "product_name": "Test Product name",
 *         "quantity": "1",
 *         "settlement_amount": "130",
 *         "revenue_amount": "200",
 *         "revenue_breakdown": {
 *           "subtotal_before_discount_amount": "210",
 *           "seller_discount_amount": "-10",
 *           "refund_subtotal_before_discount_amount": "0",
 *           "seller_discount_refund_amount": "0",
 *           "cod_service_fee_amount": "0",
 *           "refund_cod_service_fee_amount": "0"
 *         },
 *         "shipping_cost_amount": "-70",
 *         "shipping_cost_breakdown": {
 *           "actual_shipping_fee_amount": "-50",
 *           "shipping_fee_discount_amount": "0",
 *           "customer_paid_shipping_fee_amount": "0",
 *           "return_shipping_fee_amount": "-10",
 *           "replacement_shipping_fee_amount": "-10",
 *           "exchange_shipping_fee_amount": "5",
 *           "signature_confirmation_fee_amount": "5",
 *           "shipping_insurance_fee_amount": "0",
 *           "supplementary_component": {
 *             "platform_shipping_fee_discount_amount": "20",
 *             "promo_shipping_incentive_amount": "0",
 *             "shipping_fee_subsidy_amount": "0",
 *             "seller_shipping_fee_discount_amount": "0",
 *             "customer_shipping_fee_offset_amount": "-6.99",
 *             "fbm_shipping_cost_amount": "30",
 *             "fbt_shipping_cost_amount": "-30",
 *             "fbt_fulfillment_fee_amount": "0"
 *           },
 *           "return_shipping_label_fee_amount": "5"
 *         },
 *         "fee_tax_amount": "-30",
 *         "fee_tax_breakdown": {
 *           "fee": {
 *             "platform_commission_amount": "0",
 *             "referral_fee_amount": "-2",
 *             "refund_administration_fee_amount": "0",
 *             "transaction_fee_amount": "-2",
 *             "credit_card_handling_fee_amount": "0",
 *             "affiliate_commission_amount": "0",
 *             "affiliate_commission_amount_before_pit": "-2",
 *             "affiliate_partner_commission_amount": "0",
 *             "affiliate_ads_commission_amount": "0",
 *             "sfp_service_fee_amount": "2",
 *             "live_specials_fee_amount": "2",
 *             "bonus_cashback_service_fee_amount": "0",
 *             "mall_service_fee_amount": "0",
 *             "voucher_xtra_service_fee_amount": "100",
 *             "flash_sales_service_fee_amount": "50",
 *             "cofunded_promotion_service_fee_amount": "100",
 *             "pre_order_service_fee_amount": "10",
 *             "tsp_commission_amount": "10",
 *             "dt_handling_fee_amount": "10",
 *             "seller_paylater_handling_fee_amount": "-10"
 *           },
 *           "tax": {
 *             "vat_amount": "0",
 *             "import_vat_amount": "0",
 *             "customs_duty_amount": "0",
 *             "customs_clearance_amount": "0",
 *             "sst_amount": "0",
 *             "gst_amount": "0",
 *             "iva_amount": "0",
 *             "isr_amount": "0",
 *             "anti_dumping_duty_amount": "20"
 *           }
 *         }
 *       }
 *     ],
 *     "total_count": 2
 *   }
 */
@Data
public class TTSOrderRevenueResponse implements Serializable {
    private String order_id;
    private long order_create_time; // 可以根据需要使用 Date 类型
    private String currency;
    private String revenue_amount;
    private String fee_and_tax_amount;
    private String shipping_cost_amount;
    private String settlement_amount;
    private List<SkuTransaction> sku_transactions;
    private int total_count;

    @Data
    public static class SkuTransaction {
        private String sku_id;
        private String sku_name;
        private String product_name;
        private String quantity;
        private String settlement_amount;
        private String revenue_amount;
        private RevenueBreakdown revenue_breakdown;
        private String shipping_cost_amount;
        private ShippingCostBreakdown shipping_cost_breakdown;
        private String fee_tax_amount;
        private FeeTaxBreakdown fee_tax_breakdown;
    }

    @Data
    public static class RevenueBreakdown {
        private String subtotal_before_discount_amount;
        private String seller_discount_amount;
        private String refund_subtotal_before_discount_amount;
        private String seller_discount_refund_amount;
        private String cod_service_fee_amount;
        private String refund_cod_service_fee_amount;
    }

    @Data
    public static class ShippingCostBreakdown {
        private String actual_shipping_fee_amount;
        private String shipping_fee_discount_amount;
        private String customer_paid_shipping_fee_amount;
        private String return_shipping_fee_amount;
        private String replacement_shipping_fee_amount;
        private String exchange_shipping_fee_amount;
        private String signature_confirmation_fee_amount;
        private String shipping_insurance_fee_amount;
        private SupplementaryComponent supplementary_component;
        private String return_shipping_label_fee_amount;
    }

    @Data
    public static class SupplementaryComponent {
        private String platform_shipping_fee_discount_amount;
        private String promo_shipping_incentive_amount;
        private String shipping_fee_subsidy_amount;
        private String seller_shipping_fee_discount_amount;
        private String customer_shipping_fee_offset_amount;
        private String fbm_shipping_cost_amount;
        private String fbt_shipping_cost_amount;
        private String fbt_fulfillment_fee_amount;
    }

    @Data
    public static class FeeTaxBreakdown {
        private Fee fee;
        private Tax tax;
    }

    @Data
    public static class Fee {
        private String platform_commission_amount;
        private String referral_fee_amount;
        private String refund_administration_fee_amount;
        private String transaction_fee_amount;
        private String credit_card_handling_fee_amount;
        private String affiliate_commission_amount;
        private String affiliate_commission_amount_before_pit;
        private String affiliate_partner_commission_amount;
        private String affiliate_ads_commission_amount;
        private String sfp_service_fee_amount;
        private String live_specials_fee_amount;
        private String bonus_cashback_service_fee_amount;
        private String mall_service_fee_amount;
        private String voucher_xtra_service_fee_amount;
        private String flash_sales_service_fee_amount;
        private String cofunded_promotion_service_fee_amount;
        private String pre_order_service_fee_amount;
        private String tsp_commission_amount;
        private String dt_handling_fee_amount;
        private String seller_paylater_handling_fee_amount;
    }

    @Data
    public static class Tax {
        private String vat_amount;
        private String import_vat_amount;
        private String customs_duty_amount;
        private String customs_clearance_amount;
        private String sst_amount;
        private String gst_amount;
        private String iva_amount;
        private String isr_amount;
        private String anti_dumping_duty_amount;
    }
}
