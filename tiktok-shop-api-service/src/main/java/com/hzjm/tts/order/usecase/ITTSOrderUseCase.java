package com.hzjm.tts.order.usecase;

import com.hzjm.tts.order.data.TTSOrder;
import com.hzjm.tts.order.data.request.*;
import com.hzjm.tts.order.data.response.*;

import java.util.List;

public interface ITTSOrderUseCase {

    /**
     * Get order list
     * @param request
     * @param accountEmail
     * @return
     */
    List<TTSOrder> getOrderList(GetOrderListRequest request, String accountEmail);

    /**
     * Get order detail
     * @param orderId
     * @param accountEmail
     * @return
     */
    List<TTSOrder> getOrderDetail(String orderId, String accountEmail);

    /**
     * get available shipping service
     * @param orderId
     * @param request
     * @param accountEmail
     * @return
     */
    List<EligibleShippingService>  getEligibleShippingService(String orderId, GetEligibleShippingServiceRequest request, String accountEmail);

    /**
     * create shipping packages
     * @param request
     * @param accountEmail
     * @return
     */
    CreatePackageResponse createPackages(CreatePackageRequest request, String accountEmail);

    /**
     *  get shipping label
     * @param packageId
     * @param accountEmail
     * @return
     */
    GetPackageDocResponse getPackageDocument(String packageId, String accountEmail);

    /**
     * get order settlement
     * @param orderId
     * @param accountEmail
     * @return
     */
    TTSOrderRevenueResponse getOrderSettlement(String orderId, String accountEmail);

    /**
     * cancel order
     * @param request
     * @param accountEmail
     * @return
     */
    CancelOrderResponse cancelOrder(CancelOrderRequest request, String accountEmail);

    /**
     * get order return list
     * @param request
     * @param accountEmail
     * @return
     */
    List<TTSReturns> getReturnList(SearchReturnsRequest request, String accountEmail);
}
