package com.hzjm.tts.order.usecase.Impl;

import com.hzjm.common.model.BaseException;
import com.hzjm.tts.auth.TTSAuthShopManager;
import com.hzjm.tts.auth.data.response.AuthShop;
import com.hzjm.tts.common.TTSApiRequestServiceImpl;
import com.hzjm.tts.order.data.TTSOrder;
import com.hzjm.tts.order.data.request.*;
import com.hzjm.tts.order.data.response.*;
import com.hzjm.tts.order.usecase.ITTSOrderUseCase;
import com.hzjm.tts.product.TTSApiEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class TTSOrderUseCaseImpl implements ITTSOrderUseCase {

    @Autowired
    TTSApiRequestServiceImpl apiRequestService;

    @Autowired
    TTSAuthShopManager authShopManager;

    /**
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSOrder> getOrderList(GetOrderListRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Brands error, no available auth shop info to request.");
        }

        Map<String, String> query = new HashMap<>();
        query.put("shop_cipher", authShop.getCipher());
        query.put("page_size", String.valueOf(100));
        query.put("sort_order", "DESC");

        GetOrderListRequest pageRequest = request;

        String nextPageToken = null; // 分页token

        List<TTSOrder> orderList = new ArrayList<>();

        do {
            // 如果有下一页token，则设置到请求中
            if (!ObjectUtils.isEmpty(nextPageToken)) {
                query.put("page_token", nextPageToken);
            }

            // 发送请求并获取响应
            TTSOrderResponse orderResponse = apiRequestService.doRequestWithAccount(TTSApiEndpoint.getOrderList(query, request), accountEmail).getData();

            // 添加当前页的品牌到总列表中
            if (!ObjectUtils.isEmpty((orderResponse.getOrders()))) {
                orderList.addAll(orderResponse.getOrders());
            }

            // 获取下一页token
            nextPageToken = orderResponse.getNext_page_token();

        } while (nextPageToken != null && !nextPageToken.isEmpty());


        return orderList;
    }

    /**
     * @param orderId
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSOrder> getOrderDetail(String orderId, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Order Details error, no available auth shop info to request.");
        }

        Map<String, Object> query = new HashMap<>();
        query.put("ids", "[\"" + orderId + "\"]");
        query.put("shop_cipher", authShop.getCipher());

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.getOrderDetail(query), accountEmail).getData().getOrders();
    }

    /**
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public List<EligibleShippingService> getEligibleShippingService(String orderId, GetEligibleShippingServiceRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Eligible error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.getEligibleShippingService(orderId, authShop.getCipher(), request), accountEmail).getData().getShipping_services();
    }

    /**
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public CreatePackageResponse createPackages(CreatePackageRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Create TTS Package error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.createPackages(request, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * @param packageId
     * @param accountEmail
     * @return
     */
    @Override
    public GetPackageDocResponse getPackageDocument(String packageId, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Package document error, no available auth shop info to request.");
        }

        Map<String, String> query = new HashMap<>();
        query.put("document_type", "SHIPPING_LABEL");
        query.put("shop_cipher", authShop.getCipher());

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.getPackageDocument(packageId, query), accountEmail).getData();
    }

    /**
     * @param orderId
     * @param accountEmail
     * @return
     */
    @Override
    public TTSOrderRevenueResponse getOrderSettlement(String orderId, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Order settlement error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.getOrderSettlement(orderId, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public CancelOrderResponse cancelOrder(CancelOrderRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Cancel Order settlement error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.cancelOrder(request, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * get order return list
     *
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSReturns> getReturnList(SearchReturnsRequest request, String accountEmail) {

        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get returns error, no available auth shop info to request.");
        }

        Map<String, String> query = new HashMap<>();
        query.put("shop_cipher", authShop.getCipher());
        query.put("page_size", String.valueOf(50));
        query.put("sort_order", "DESC");
        query.put("sort_field", "create_time");

        String nextPageToken = null; // 分页token

        List<TTSReturns> orderList = new ArrayList<>();

        do {
            // 如果有下一页token，则设置到请求中
            if (!ObjectUtils.isEmpty(nextPageToken)) {
                query.put("page_token", nextPageToken);
            }

            // 发送请求并获取响应
            ReturnsResponse orderResponse = apiRequestService.doRequestWithAccount(TTSApiEndpoint.getReturnsList(query, request), accountEmail).getData();

            // 添加当前页的品牌到总列表中
            if (!ObjectUtils.isEmpty((orderResponse.getReturn_orders()))) {
                orderList.addAll(orderResponse.getReturn_orders());
            }

            // 获取下一页token
            nextPageToken = orderResponse.getNext_page_token();

        } while (nextPageToken != null && !nextPageToken.isEmpty());

        return orderList;
    }

}
