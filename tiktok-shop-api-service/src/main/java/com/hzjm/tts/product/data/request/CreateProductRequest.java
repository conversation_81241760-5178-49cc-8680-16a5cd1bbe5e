package com.hzjm.tts.product.data.request;

import com.hzjm.tts.product.data.ProductSaveMode;
import com.hzjm.tts.product.data.response.PackageDimension;
import com.hzjm.tts.product.data.response.PackageWeight;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * {
 *     "skus": [
 *         {
 *             "price": {
 *                 "amount": "10",
 *                 "currency": "USD"
 *             },
 *             "sales_attributes": [
 *                 {
 *                     "id": "100000",
 *                     "name": "Color",
 *                     "value_name": "Red",
 *                     "sku_img": {
 *                         "uri": "tos-useast5-i-omjb5zjo8w-tx/d9d9c89b25d6478d9e454c118d9b0a5d"
 *                     }
 *                 },
 *                 {
 *                     "name": "Size",
 *                     "value_name": "Small",
 *                     "is_custom": true
 *                 }
 *             ],
 *             "inventory": [
 *                 {
 *                     "warehouse_id": "7341080923485996842",
 *                     "quantity": 10
 *                 }
 *             ],
 *             "seller_sku": "R-SM-1"
 *         },
 *         {
 *             "price": {
 *                 "amount": "10",
 *                 "currency": "USD"
 *             },
 *             "sales_attributes": [
 *                 {
 *                     "id": "100000",
 *                     "name": "Color",
 *                     "value_name": "Red",
 *                     "sku_img": {
 *                         "uri": "tos-useast5-i-omjb5zjo8w-tx/d9d9c89b25d6478d9e454c118d9b0a5d"
 *                     }
 *                 },
 *                 {
 *                     "name": "Size",
 *                     "value_name": "Large",
 *                     "is_custom": true
 *                 }
 *             ],
 *             "inventory": [
 *                 {
 *                     "warehouse_id": "7341080923485996842",
 *                     "quantity": 10
 *                 }
 *             ],
 *             "seller_sku": "R-LG-1"
 *         },
 *         {
 *             "price": {
 *                 "amount": "10",
 *                 "currency": "USD"
 *             },
 *             "sales_attributes": [
 *                 {
 *                     "id": "100000",
 *                     "name": "Color",
 *                     "value_name": "Blue",
 *                     "sku_img": {
 *                         "uri": "tos-useast5-i-omjb5zjo8w-tx/d9d9c89b25d6478d9e454c118d9b0a5d"
 *                     }
 *                 },
 *                 {
 *                     "name": "Size",
 *                     "value_name": "Small",
 *                     "is_custom": true
 *                 }
 *             ],
 *             "inventory": [
 *                 {
 *                     "warehouse_id": "7341080923485996842",
 *                     "quantity": 10
 *                 }
 *             ],
 *             "seller_sku": "B-SM-1"
 *         },
 *         {
 *             "price": {
 *                 "amount": "10",
 *                 "currency": "USD"
 *             },
 *             "sales_attributes": [
 *                 {
 *                     "id": "100000",
 *                     "name": "Color",
 *                     "value_name": "Blue"
 *                 },
 *                 {
 *                     "name": "Size",
 *                     "value_name": "Large",
 *                     "is_custom": true
 *                 }
 *             ],
 *             "inventory": [
 *                 {
 *                     "warehouse_id": "7341080923485996842",
 *                     "quantity": 10
 *                 }
 *             ],
 *             "seller_sku": "B-LG-1"
 *         }
 *     ],
 *     "package_dimensions": {
 *         "length": "1",
 *         "width": "1",
 *         "height": "1",
 *         "unit": "INCH"
 *     },
 *     "title": "HazNat API Product Test",
 *     "description": "This product was created using the API for testing purposes",
 *     "category_id": "874504",
 *     "package_weight": {
 *         "value": "1",
 *         "unit": "POUND"
 *     },
 *     "main_images": [
 *         {
 *             "uri": "tos-useast5-i-omjb5zjo8w-tx/feba0a07377544d985144b62b026a947"
 *         }
 *     ],
 *     "certifications": [
 *         {
 *             "id": "7216311839301388037",
 *             "images": [
 *                 {
 *                     "uri": "tos-useast5-i-omjb5zjo8w-tx/feba0a07377544d985144b62b026a947"
 *                 }
 *             ]
 *         },
 *         {
 *             "id": "7216311839301404421",
 *             "images": [
 *                 {
 *                     "uri": "tos-useast5-i-omjb5zjo8w-tx/feba0a07377544d985144b62b026a947"
 *                 }
 *             ]
 *         }
 *     ],
 *     "brand_id": "7277482095260501766"
 * }
 */
@Data
@NoArgsConstructor
public class CreateProductRequest implements Serializable {

    ProductSaveMode saveMode;

    @ApiModelProperty("商品描述, 但必须是 HTML 格式")
    @NonNull
    String description;

    // 这个参数需要 get category 接口获取
    @NonNull
    String category_id;

    // 品牌ID, 需要 get brand 接口获取
    @NonNull
    String brand_id;

    @ApiModelProperty("Poizon 商品主图")
    String sample_img;

    // 商品主图片
    @NonNull
    List<Uri> main_images;

    // 商品 skus 在这里 并不是我们系统的 sku 而是 我们系统的 sku + size ，精确到码数 为 sku
    @NonNull
    List<TTSSku> skus;

    @NonNull
    String title;

    boolean is_cod_allowed;

    String external_product_id;

    @NonNull
    PackageWeight package_weight;

    @NonNull
    PackageDimension package_dimensions;

    String category_version = "v2";

    private CreateProductRequest(Builder builder) {
        this.saveMode = builder.saveMode;
        this.description = builder.description;
        this.category_id = builder.category_id;
        this.brand_id = builder.brand_id;
        this.sample_img = builder.sample_img;
        this.main_images = builder.main_images;
        this.skus = builder.skus;
        this.title = builder.title;
        this.is_cod_allowed = builder.is_cod_allowed;
        this.external_product_id = builder.external_product_id;
        this.package_weight = builder.package_weight;
        this.package_dimensions = builder.package_dimensions;
    }

    public static class Builder {
        private ProductSaveMode saveMode;
        private String description;
        private String category_id;
        private String brand_id;
        private String sample_img;
        private List<Uri> main_images = new ArrayList<>();
        private List<TTSSku> skus = new ArrayList<>();
        private String title;
        private boolean is_cod_allowed;
        private String external_product_id;
        private PackageWeight package_weight;
        private PackageDimension package_dimensions;

        public Builder saveMode(ProductSaveMode saveMode) {
            this.saveMode = saveMode;
            return this;
        }

        public Builder description(@NonNull String description) {
            this.description = description;
            return this;
        }

        public Builder categoryId(@NonNull String category_id) {
            this.category_id = category_id;
            return this;
        }

        public Builder brandId(@NonNull String brand_id) {
            this.brand_id = brand_id;
            return this;
        }

        public Builder sampleImg(String sample_img) {
            this.sample_img = sample_img;
            return this;
        }

        public Builder mainImages(@NonNull List<Uri> main_images) {
            this.main_images = main_images;
            return this;
        }

        public Builder skus(@NonNull List<TTSSku> skus) {
            this.skus = skus;
            return this;
        }

        public Builder title(@NonNull String title) {
            this.title = title;
            return this;
        }

        public Builder isCodAllowed(boolean is_cod_allowed) {
            this.is_cod_allowed = is_cod_allowed;
            return this;
        }

        public Builder externalProductId(String external_product_id) {
            this.external_product_id = external_product_id;
            return this;
        }

        public Builder packageWeight(@NonNull PackageWeight package_weight) {
            this.package_weight = package_weight;
            return this;
        }

        public Builder packageDimension(@NonNull PackageDimension package_dimension) {
            this.package_dimensions = package_dimension;
            return this;
        }

        public CreateProductRequest build() {
            // 在此处确保所有必需属性不为 null
            if (description == null) throw new IllegalArgumentException("description cannot be null");
            if (category_id == null) throw new IllegalArgumentException("category_id cannot be null");
            if (brand_id == null) throw new IllegalArgumentException("brand_id cannot be null");
            if (main_images == null) throw new IllegalArgumentException("main_images cannot be null or empty");
            if (skus == null || skus.isEmpty()) throw new IllegalArgumentException("skus cannot be null or empty");
            if (title == null) throw new IllegalArgumentException("title cannot be null");
            if (package_weight == null) throw new IllegalArgumentException("package_weight cannot be null");
            if (package_dimensions == null) throw new IllegalArgumentException("package_dimension cannot be null");

            return new CreateProductRequest(this);
        }
    }

    @Data
    public static class Uri implements Serializable {
        String uri;
    }

}
