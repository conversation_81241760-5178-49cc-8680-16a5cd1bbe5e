package com.hzjm.tts.product.data.request;

import com.hzjm.tts.common.BasePaginationRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetBrandsRequest extends BasePaginationRequest implements Serializable {
    String shop_cipher;
    String category_version;
    int page_size = 100;
}
