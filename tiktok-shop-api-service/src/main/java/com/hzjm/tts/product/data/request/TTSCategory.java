package com.hzjm.tts.product.data.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {
 *         "id": "600002",
 *         "is_leaf": false,
 *         "local_name": "Home Supplies",
 *         "parent_id": "600001",
 *         "permission_statuses": [
 *           "INVITE_ONLY",
 *           "NON_MAIN_CATEGORY"
 *         ]
 *       }
 */
@Data
public class TTSCategory implements Serializable {
    private String id;
    private boolean isLeaf;
    private String localName;
    private String parentId;
    private List<String> permissionStatuses;

}
