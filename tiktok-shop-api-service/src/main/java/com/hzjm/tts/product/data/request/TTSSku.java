package com.hzjm.tts.product.data.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class TTSSku implements Serializable {

    @ApiModelProperty("这里 就是 TTS SKu 每个鞋款唯一鞋码的 唯一ID， 从创建开始 就不能改变，因为 TTS 的 逻辑 在 修改接口 必须提供 此 ID")
    String id;

    List<SalesAttributes> sales_attributes;

    @NonNull
    List<Inventory> inventory;

    String seller_sku;

    @NonNull
    Price price;

    String external_sku_id;

    String sku_unit_count;

    /**
     * Private constructor to enforce the use of Builder
     */
    private TTSSku(Builder builder) {
        this.id = builder.id;
        this.sales_attributes = builder.sales_attributes;
        this.inventory = builder.inventory;
        this.seller_sku = builder.seller_sku;
        this.price = builder.price;
        this.external_sku_id = builder.external_sku_id;
        this.sku_unit_count = builder.sku_unit_count;
    }

    public static class Builder {
        String id;
        private List<SalesAttributes> sales_attributes = new ArrayList<>();
        private List<Inventory> inventory;
        private String seller_sku;
        private Price price;
        private String external_sku_id;
        private String sku_unit_count;

        public Builder id(String id) {
            this.id = id;
            return this;
        }

        public Builder salesAttributes(List<SalesAttributes> sales_attributes) {
            this.sales_attributes = sales_attributes;
            return this;
        }

        public Builder inventory(@NonNull List<Inventory> inventory) {
            this.inventory = inventory;
            return this;
        }

        public Builder sellerSku(String seller_sku) {
            this.seller_sku = seller_sku;
            return this;
        }

        public Builder price(@NonNull Price price) {
            this.price = price;
            return this;
        }

        public Builder externalSkuId(String external_sku_id) {
            this.external_sku_id = external_sku_id;
            return this;
        }

        public Builder skuUnitCount(String sku_unit_count) {
            this.sku_unit_count = sku_unit_count;
            return this;
        }

        public TTSSku build() {
            // 在构建时检查必要字段
            if (inventory == null || inventory.isEmpty()) {
                throw new IllegalArgumentException("inventory cannot be null or empty");
            }

            if (price == null) {
                throw new IllegalArgumentException("price cannot be null");
            }

            return new TTSSku(this);
        }
    }

    /**
     * A list of attributes  (e.g. size, color, length) that define each variant of a product.
     *
     * Note:
     * - If your product is straightforward without any sales attributes, you can omit this object.
     * - You can only have up to 3 types of sales attributes per product.
     * - Each SKU must include the same number and type of sales attributes. For example, you cannot have one SKU that has only a Color attribute, while another SKU has both Color and Size attributes.
     * - Provide either a built-in ID or a custom name; if both are provided, the ID takes priority.
     * - The id/name and value_id/value_name pairs must be unique in each SKU. For example, you cannot repeat "name": "Color", "value_name": "Red" in different SKUs
     */
    @Data
    public static class SalesAttributes implements Serializable {
        String id;
        String value_id;

        @ApiModelProperty("如果 为 size 则为尺码，格式可以是: US 7M / 8.5W， 如果是颜色 ，格式可以是: Sea Salt/White")
        String value_name;

        SkuImage sku_img;

        @ApiModelProperty("可以填写 为 Size 或者 Color")
        String name;

        @Data
        public static class SkuImage implements Serializable {
            String uri;
        }
    }

    /**
     * The inventory of the product.
     */
    @Data
    @AllArgsConstructor
    public static class Inventory implements Serializable {
        @NonNull
        String warehouse_id;
        Integer quantity;
    }

    /**
     * The price of the product.
     */
    @Data
    @AllArgsConstructor
    public static class Price implements Serializable {
        @NonNull
        String amount;
        String currency = "USD";
    }
}
